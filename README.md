# Tournament Management System

A complete tournament management system with a FastAPI backend, MongoDB database, and Valorant-themed frontend.

## 🏗️ Project Structure

```
├── backend/                 # FastAPI backend
│   ├── main.py             # Main application file
│   ├── models.py           # Pydantic models
│   ├── utils.py            # Utility functions
│   ├── requirements.txt    # Python dependencies
│   ├── .env               # Environment variables
│   └── render.yaml        # Render.com deployment config
├── admin-panel/            # Admin panel (static site)
│   ├── index.html         # Admin dashboard
│   ├── styles.css         # Admin panel styles
│   └── script.js          # Admin panel functionality
├── team-ap/               # Public website (static site)
│   ├── index.html         # Home page
│   ├── schedule.html      # Schedule page
│   ├── brackets.html      # Brackets page
│   ├── history.html       # History page
│   ├── styles.css         # Valorant-themed styles
│   ├── script.js          # Home page functionality
│   ├── schedule.js        # Schedule page functionality
│   ├── brackets.js        # Brackets page functionality
│   └── history.js         # History page functionality
└── README.md              # This file
```

## 🚀 Quick Start

### Backend Setup

1. **Navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   - Copy `.env.example` to `.env`
   - Update the MongoDB connection string if needed
   - Change the SECRET_KEY for production

4. **Run the backend:**
   ```bash
   python main.py
   ```
   
   The API will be available at `http://localhost:8000`

### Frontend Setup

1. **Admin Panel:**
   - Open `admin-panel/index.html` in a web browser
   - Default credentials: `admin` / `admin123`

2. **Public Website (team-ap):**
   - Open `team-ap/index.html` in a web browser
   - Or serve it using a local web server

### Using a Local Web Server

For better development experience, serve the frontend using a local web server:

```bash
# Using Python
python -m http.server 3000

# Using Node.js (if you have it installed)
npx serve .

# Using PHP (if you have it installed)
php -S localhost:3000
```

## 🔧 Configuration

### Backend Configuration

Update the following in `backend/.env`:

```env
MONGODB_URL=your-mongodb-connection-string
DATABASE_NAME=tournament_management
SECRET_KEY=your-secret-key-here
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
PORT=8000
```

### Frontend Configuration

Update the API base URL in the JavaScript files:

- `admin-panel/script.js`
- `team-ap/script.js`
- `team-ap/schedule.js`
- `team-ap/brackets.js`
- `team-ap/history.js`

Change this line in each file:
```javascript
const API_BASE_URL = 'http://localhost:8000'; // Change to your deployed backend URL
```

## 🌐 Deployment

### Backend Deployment (Render.com)

1. **Push your code to a Git repository**

2. **Create a new Web Service on Render.com:**
   - Connect your repository
   - Use the `backend` directory as the root
   - Render will automatically detect the `render.yaml` file

3. **Set environment variables on Render:**
   - `MONGODB_URL`: Your MongoDB Atlas connection string
   - `SECRET_KEY`: A secure secret key
   - `ADMIN_USERNAME`: Admin username
   - `ADMIN_PASSWORD`: Admin password

### Frontend Deployment

#### Admin Panel
Deploy the `admin-panel` folder to any static hosting service:
- Netlify
- Vercel
- GitHub Pages
- Render Static Sites

#### Public Website (team-ap)
Deploy the `team-ap` folder to any static hosting service:
- Netlify
- Vercel
- GitHub Pages
- Render Static Sites

## 📱 Features

### Admin Panel Features
- **Authentication:** Token-based login with 24-hour sessions
- **Tournament Management:** Create, edit, and manage tournaments
- **Team Management:** Add teams with members and icons
- **Match Scheduling:** Schedule matches with IST timezone support
- **Bracket Management:** Create and manage tournament brackets
- **Live Tournament Control:** Set tournaments as live/completed

### Public Website Features
- **Valorant Theme:** Custom Valorant-inspired design
- **Home Page:** Current tournament info and recent matches
- **Schedule Page:** Filtered match listings (all, upcoming, live, completed)
- **Brackets Page:** Tournament bracket visualization
- **History Page:** Past tournament records with detailed views
- **Responsive Design:** Mobile-friendly interface

## 🔐 Authentication

### Admin Panel
- Hardcoded credentials stored in frontend
- JWT tokens with 24-hour expiration
- Automatic logout on token expiry

### API Security
- All write operations require valid JWT token
- CORS enabled for frontend domains
- Input validation using Pydantic models

## 🕐 Timezone Handling

- All times stored in UTC in the database
- Converted to IST (Indian Standard Time) for display
- Automatic timezone conversion in API responses

## 🎮 Valorant Theme

The public website features a custom Valorant-inspired design with:
- Valorant color palette (red, blue, gold, cyan)
- Custom fonts (Rajdhani)
- Animated elements and hover effects
- Responsive design for all devices

## 🛠️ API Endpoints

### Authentication
- `POST /api/login` - Admin login

### Tournaments
- `GET /api/tournaments/live` - Get live tournament
- `GET /api/tournaments/completed` - Get completed tournaments
- `POST /api/tournaments` - Create tournament
- `PATCH /api/tournaments/{id}/status` - Update tournament status

### Teams
- `GET /api/tournaments/{id}/teams` - Get tournament teams
- `POST /api/teams` - Create team
- `PATCH /api/teams/{id}` - Update team
- `DELETE /api/teams/{id}` - Delete team

### Matches
- `GET /api/tournaments/{id}/matches` - Get tournament matches
- `POST /api/matches` - Create match
- `PATCH /api/matches/{id}` - Update match

### Brackets
- `GET /api/brackets/{tournament_id}` - Get tournament brackets
- `POST /api/brackets/{tournament_id}` - Create bracket

## 🗄️ Database Schema

### Tournaments
```json
{
  "_id": "ObjectId",
  "name": "Tournament Name",
  "type": "Single Elimination",
  "status": "live|upcoming|completed",
  "start_date": "ISODate",
  "prize_pool": 25000,
  "created_at": "ISODate"
}
```

### Teams
```json
{
  "_id": "ObjectId",
  "tournament_id": "ObjectId",
  "name": "Team Name",
  "members": ["Player1", "Player2"],
  "icon": "https://icon-url.png",
  "initials": "TN"
}
```

### Matches
```json
{
  "_id": "ObjectId",
  "tournament_id": "ObjectId",
  "teamA": "ObjectId",
  "teamB": "ObjectId",
  "start_time": "ISODate",
  "status": "scheduled|completed",
  "scoreA": 13,
  "scoreB": 8,
  "winner": "ObjectId",
  "match_id": "M001"
}
```

### Brackets
```json
{
  "_id": "ObjectId",
  "tournament_id": "ObjectId",
  "round": 1,
  "matchups": [
    {
      "teamA": "ObjectId",
      "teamB": "ObjectId", 
      "winner": "ObjectId"
    }
  ]
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is created by **shushie** for tournament management purposes.

## 🆘 Support

For issues and questions:
1. Check the console for error messages
2. Verify API connectivity
3. Ensure MongoDB connection is working
4. Check environment variables are set correctly

---

**Created by shushie** 🎮
