from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from bson import ObjectId

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)

    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")

# Authentication models
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

# Tournament models
class TournamentCreate(BaseModel):
    name: str
    type: str = "Single Elimination"
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    prize_pool: Optional[int] = 0
    status: str = "upcoming"
    notes: Optional[str] = None  # Tournament-specific notes

class TournamentResponse(BaseModel):
    id: str = Field(alias="_id")
    name: str
    type: str
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    prize_pool: Optional[int] = 0
    status: str
    created_at: datetime
    notes: Optional[str] = None  # Tournament-specific notes

    class Config:
        populate_by_name = True

class StatusUpdate(BaseModel):
    status: str

# Player model
class Player(BaseModel):
    name: str
    role: Optional[str] = None
    rank: Optional[str] = None  # Valorant rank image filename (e.g., "Diamond_2_Rank.png")

# Team models
class TeamCreate(BaseModel):
    tournament_id: str
    name: str
    members: List[str]  # Keep for backward compatibility
    players: Optional[List[Player]] = []  # New detailed player structure
    icon: Optional[str] = None
    initials: Optional[str] = None

class TeamResponse(BaseModel):
    id: str = Field(alias="_id")
    tournament_id: str
    name: str
    members: List[str]  # Keep for backward compatibility
    players: Optional[List[Player]] = []  # New detailed player structure
    icon: Optional[str] = None
    initials: Optional[str] = None

    class Config:
        populate_by_name = True

class TeamUpdate(BaseModel):
    name: Optional[str] = None
    members: Optional[List[str]] = None  # Keep for backward compatibility
    players: Optional[List[Player]] = None  # New detailed player structure
    icon: Optional[str] = None
    initials: Optional[str] = None

# Match models
class MatchCreate(BaseModel):
    tournament_id: str
    teamA: str
    teamB: str
    start_time: datetime
    match_id: Optional[str] = None

class MatchResponse(BaseModel):
    id: str = Field(alias="_id")
    tournament_id: str
    teamA: str
    teamB: str
    start_time: datetime
    status: str = "scheduled"
    scoreA: Optional[int] = None
    scoreB: Optional[int] = None
    winner: Optional[str] = None
    match_id: Optional[str] = None

    class Config:
        populate_by_name = True

class MatchUpdate(BaseModel):
    teamA: Optional[str] = None
    teamB: Optional[str] = None
    start_time: Optional[datetime] = None
    status: Optional[str] = None
    scoreA: Optional[int] = None
    scoreB: Optional[int] = None
    winner: Optional[str] = None

# Bracket models
class Matchup(BaseModel):
    teamA: Optional[str] = None
    teamB: Optional[str] = None
    winner: Optional[str] = None

class BracketCreate(BaseModel):
    tournament_id: str
    round: int
    round_name: Optional[str] = None  # e.g., "Quarterfinals", "Grand Finals"
    matchups: List[Matchup]

class BracketUpdate(BaseModel):
    round: Optional[int] = None
    round_name: Optional[str] = None
    matchups: Optional[List[Matchup]] = None

class BracketResponse(BaseModel):
    id: str = Field(alias="_id")
    tournament_id: str
    round: int
    round_name: Optional[str] = None  # e.g., "Quarterfinals", "Grand Finals"
    matchups: List[Matchup]

    class Config:
        populate_by_name = True

# Valorant Ranks Configuration
VALORANT_RANKS = [
    {"name": "Iron 1", "filename": "Iron_1_Rank.png", "tier": 1},
    {"name": "Iron 2", "filename": "Iron_2_Rank.png", "tier": 2},
    {"name": "Iron 3", "filename": "Iron_3_Rank.png", "tier": 3},
    {"name": "Bronze 1", "filename": "Bronze_1_Rank.png", "tier": 4},
    {"name": "Bronze 2", "filename": "Bronze_2_Rank.png", "tier": 5},
    {"name": "Bronze 3", "filename": "Bronze_3_Rank.png", "tier": 6},
    {"name": "Silver 1", "filename": "Silver_1_Rank.png", "tier": 7},
    {"name": "Silver 2", "filename": "Silver_2_Rank.png", "tier": 8},
    {"name": "Silver 3", "filename": "Silver_3_Rank.png", "tier": 9},
    {"name": "Gold 1", "filename": "Gold_1_Rank.png", "tier": 10},
    {"name": "Gold 2", "filename": "Gold_2_Rank.png", "tier": 11},
    {"name": "Gold 3", "filename": "Gold_3_Rank.png", "tier": 12},
    {"name": "Platinum 1", "filename": "Platinum_1_Rank.png", "tier": 13},
    {"name": "Platinum 2", "filename": "Platinum_2_Rank.png", "tier": 14},
    {"name": "Platinum 3", "filename": "Platinum_3_Rank.png", "tier": 15},
    {"name": "Diamond 1", "filename": "Diamond_1_Rank.png", "tier": 16},
    {"name": "Diamond 2", "filename": "Diamond_2_Rank.png", "tier": 17},
    {"name": "Diamond 3", "filename": "Diamond_3_Rank.png", "tier": 18},
    {"name": "Ascendant 1", "filename": "Ascendant_1_Rank.png", "tier": 19},
    {"name": "Ascendant 2", "filename": "Ascendant_2_Rank.png", "tier": 20},
    {"name": "Ascendant 3", "filename": "Ascendant_3_Rank.png", "tier": 21},
    {"name": "Immortal 1", "filename": "Immortal_1_Rank.png", "tier": 22},
    {"name": "Immortal 2", "filename": "Immortal_2_Rank.png", "tier": 23},
    {"name": "Immortal 3", "filename": "Immortal_3_Rank.png", "tier": 24},
    {"name": "Radiant", "filename": "Radiant_Rank.png", "tier": 25}
]
