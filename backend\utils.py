from datetime import datetime
import pytz
from bson import ObjectId

# IST timezone
IST = pytz.timezone('Asia/Kolkata')
UTC = pytz.timezone('UTC')

def convert_ist_to_utc(ist_datetime):
    """Convert IST datetime to UTC"""
    if isinstance(ist_datetime, str):
        ist_datetime = datetime.fromisoformat(ist_datetime.replace('Z', '+00:00'))
    
    if ist_datetime.tzinfo is None:
        # Assume it's IST if no timezone info
        ist_datetime = IST.localize(ist_datetime)
    
    return ist_datetime.astimezone(UTC).replace(tzinfo=None)

def convert_utc_to_ist(utc_datetime):
    """Convert UTC datetime to IST"""
    if utc_datetime is None:
        return None
    
    if utc_datetime.tzinfo is None:
        utc_datetime = UTC.localize(utc_datetime)
    
    return utc_datetime.astimezone(IST)

def convert_tournament_response(tournament):
    """Convert tournament document to response format"""
    if not tournament:
        return None

    # Convert _id to id and make it a string
    tournament["id"] = str(tournament["_id"])
    del tournament["_id"]

    # Convert start_date to IST for response
    if tournament.get("start_date"):
        tournament["start_date"] = convert_utc_to_ist(tournament["start_date"])

    # Convert end_date to IST for response
    if tournament.get("end_date"):
        tournament["end_date"] = convert_utc_to_ist(tournament["end_date"])

    return tournament

def convert_team_response(team):
    """Convert team document to response format"""
    if not team:
        return None

    # Convert _id to id and make it a string
    team["id"] = str(team["_id"])
    del team["_id"]
    return team

def convert_match_response(match):
    """Convert match document to response format"""
    if not match:
        return None

    # Convert _id to id and make it a string
    match["id"] = str(match["_id"])
    del match["_id"]

    # Convert start_time to IST for response
    if match.get("start_time"):
        match["start_time"] = convert_utc_to_ist(match["start_time"])

    return match

def convert_bracket_response(bracket):
    """Convert bracket document to response format"""
    if not bracket:
        return None

    # Convert _id to id and make it a string
    bracket["id"] = str(bracket["_id"])
    del bracket["_id"]
    return bracket

def generate_initials(team_name):
    """Generate initials from team name"""
    words = team_name.split()
    if len(words) == 1:
        return words[0][:2].upper()
    else:
        return ''.join([word[0].upper() for word in words[:2]])
