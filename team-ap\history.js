// Configuration
const API_BASE_URL = 'http://localhost:8000'; // Change this to your deployed backend URL

// Global state
let completedTournaments = [];

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadData();
});

// Setup event listeners
function setupEventListeners() {
    // Mobile menu toggle
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
}

// API helper function
async function apiCall(endpoint) {
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        
        if (response.ok) {
            return await response.json();
        } else {
            console.error(`API call failed: ${response.status} ${response.statusText}`);
            return null;
        }
    } catch (error) {
        console.error('API call error:', error);
        return null;
    }
}

// Load all data
async function loadData() {
    await loadCompletedTournaments();
    displayTournamentHistory();
}

// Load completed tournaments
async function loadCompletedTournaments() {
    completedTournaments = await apiCall('/api/tournaments/completed') || [];
    // Sort by creation date (most recent first)
    completedTournaments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
}

// Display tournament history
function displayTournamentHistory() {
    const historyContainer = document.getElementById('tournamentHistory');
    
    if (!completedTournaments || completedTournaments.length === 0) {
        historyContainer.innerHTML = `
            <div class="no-history">
                <h2>No Tournament History</h2>
                <p>No completed tournaments found. Check back after some tournaments are finished!</p>
            </div>
        `;
        return;
    }
    
    historyContainer.innerHTML = `
        <div class="history-grid">
            ${completedTournaments.map(tournament => `
                <div class="history-card" data-tournament-id="${tournament.id}">
                    <div class="history-card-header">
                        <h3 class="tournament-name">${tournament.name}</h3>
                        <div class="tournament-date">${formatDate(tournament.created_at)}</div>
                    </div>
                    
                    <div class="tournament-info">
                        <div class="info-item">
                            <span class="info-label">Type:</span>
                            <span class="info-value">${tournament.type}</span>
                        </div>
                        
                        ${tournament.prize_pool ? `
                            <div class="info-item">
                                <span class="info-label">Prize Pool:</span>
                                <span class="info-value prize">₹${tournament.prize_pool.toLocaleString()}</span>
                            </div>
                        ` : ''}
                        
                        ${tournament.start_date ? `
                            <div class="info-item">
                                <span class="info-label">Start Date:</span>
                                <span class="info-value">${formatDate(tournament.start_date)}</span>
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="tournament-status completed">COMPLETED</div>
                    
                    <div class="card-actions">
                        <button class="view-details-btn" onclick="viewTournamentDetails('${tournament.id}')">
                            View Details
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// View tournament details
async function viewTournamentDetails(tournamentId) {
    try {
        // Load tournament data
        const [teams, matches, brackets] = await Promise.all([
            apiCall(`/api/tournaments/${tournamentId}/teams`),
            apiCall(`/api/tournaments/${tournamentId}/matches`),
            apiCall(`/api/brackets/${tournamentId}`)
        ]);
        
        const tournament = completedTournaments.find(t => t.id === tournamentId);
        
        // Find the winner (team that won the most matches or final bracket winner)
        let winner = null;
        if (matches && matches.length > 0) {
            const completedMatches = matches.filter(m => m.status === 'completed' && m.winner);
            if (completedMatches.length > 0) {
                // Count wins for each team
                const winCounts = {};
                completedMatches.forEach(match => {
                    winCounts[match.winner] = (winCounts[match.winner] || 0) + 1;
                });
                
                // Find team with most wins
                const winnerTeamId = Object.keys(winCounts).reduce((a, b) => 
                    winCounts[a] > winCounts[b] ? a : b
                );
                winner = teams?.find(t => t.id === winnerTeamId);
            }
        }
        
        // Create modal content
        const modalContent = `
            <div class="tournament-details-modal">
                <div class="modal-header">
                    <h2>${tournament.name}</h2>
                    <span class="close-modal" onclick="closeModal()">&times;</span>
                </div>
                
                <div class="modal-body">
                    <div class="tournament-summary">
                        <div class="summary-item">
                            <h3>Tournament Type</h3>
                            <p>${tournament.type}</p>
                        </div>
                        
                        ${tournament.prize_pool ? `
                            <div class="summary-item">
                                <h3>Prize Pool</h3>
                                <p class="prize">₹${tournament.prize_pool.toLocaleString()}</p>
                            </div>
                        ` : ''}
                        
                        ${winner ? `
                            <div class="summary-item">
                                <h3>Champion</h3>
                                <p class="winner">🏆 ${winner.name}</p>
                            </div>
                        ` : ''}
                        
                        <div class="summary-item">
                            <h3>Participants</h3>
                            <p>${teams?.length || 0} Teams</p>
                        </div>
                        
                        <div class="summary-item">
                            <h3>Total Matches</h3>
                            <p>${matches?.length || 0} Matches</p>
                        </div>
                    </div>
                    
                    ${teams && teams.length > 0 ? `
                        <div class="teams-section">
                            <h3>Participating Teams</h3>
                            <div class="teams-grid">
                                ${teams.map(team => {
                                    // Create player list with ranks
                                    let playersDisplay = '';
                                    if (team.players && team.players.length > 0) {
                                        playersDisplay = team.players.slice(0, 3).map(player => {
                                            const rankImg = player.rank ?
                                                `<img src="rank_png/${player.rank}" alt="Rank" class="rank-icon">` : '';
                                            return `${rankImg}${player.name}`;
                                        }).join(', ');
                                        if (team.players.length > 3) {
                                            playersDisplay += ` +${team.players.length - 3} more`;
                                        }
                                    } else if (team.members && team.members.length > 0) {
                                        playersDisplay = team.members.slice(0, 3).join(', ');
                                        if (team.members.length > 3) {
                                            playersDisplay += ` +${team.members.length - 3} more`;
                                        }
                                    }

                                    return `
                                        <div class="team-card ${winner && winner.id === team.id ? 'champion' : ''}">
                                            <div class="team-name">${team.name}</div>
                                            <div class="team-initials">${team.initials}</div>
                                            ${playersDisplay ? `<div class="team-players">${playersDisplay}</div>` : ''}
                                            ${winner && winner.id === team.id ? '<div class="champion-badge">🏆 CHAMPION</div>' : ''}
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
        
        // Show modal
        showModal(modalContent);
        
    } catch (error) {
        console.error('Error loading tournament details:', error);
        alert('Failed to load tournament details. Please try again.');
    }
}

// Show modal
function showModal(content) {
    // Remove existing modal if any
    const existingModal = document.querySelector('.modal-overlay');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            ${content}
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeModal();
        }
    });
}

// Close modal
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}
