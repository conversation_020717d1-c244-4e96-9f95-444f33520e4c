<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team AP - Valorant Tournament Hub</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h1>TEAM-<span class="accent">AP</span></h1>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="index.html" class="nav-link active">HOME</a>
                </li>
                <li class="nav-item">
                    <a href="schedule.html" class="nav-link">SCHEDULE</a>
                </li>
                <li class="nav-item">
                    <a href="brackets.html" class="nav-link">BRACKETS</a>
                </li>
                <li class="nav-item">
                    <a href="history.html" class="nav-link">HISTORY</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1 class="hero-title" id="tournament-name">LOADING...</h1>
            <h2 class="hero-subtitle" id="tournament-subtitle">LOADING...</h2>
            <p class="hero-description" id="tournament-description">Loading tournament information...</p>
            <div class="hero-stats">
                <div class="stat">
                    <span class="stat-number" id="teams-count">-</span>
                    <span class="stat-label">TEAMS</span>
                </div>
                <div class="stat">
                    <span class="stat-number" id="prize-pool">-</span>
                    <span class="stat-label">PRIZE POOL</span>
                </div>
                <div class="stat">
                    <span class="stat-number" id="tournament-days">-</span>
                    <span class="stat-label" id="tournament-days-label">DAYS</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Upcoming Matches -->
    <section class="upcoming-matches">
        <div class="container">
            <h2 class="section-title">UPCOMING MATCHES</h2>
            <div class="matches-grid" id="upcoming-matches">
                <div class="loading">Loading upcoming matches...</div>
            </div>
        </div>
    </section>

    <!-- Recent Results -->
    <section class="recent-results">
        <div class="container">
            <h2 class="section-title">RECENT RESULTS</h2>
            <div class="results-grid" id="recent-results">
                <div class="loading">Loading recent results...</div>
            </div>
        </div>
    </section>

    <!-- Tournament Info -->
    <section class="tournament-info">
        <div class="container">
            <div class="info-grid" id="tournament-info-grid">
                <div class="loading">Loading tournament details...</div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Team-AP Valorant Tournaments. Created by shushie. Not affiliated with Riot Games.</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
