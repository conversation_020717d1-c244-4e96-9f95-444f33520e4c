// Configuration
const API_BASE_URL = 'http://localhost:8000'; // Change this to your deployed backend URL

// Global state
let currentTournament = null;
let teams = [];
let matches = [];

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadData();
});

// Setup event listeners
function setupEventListeners() {
    // Mobile menu toggle
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }
}

// API helper function
async function apiCall(endpoint) {
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        
        if (response.ok) {
            return await response.json();
        } else {
            console.error(`API call failed: ${response.status} ${response.statusText}`);
            return null;
        }
    } catch (error) {
        console.error('API call error:', error);
        return null;
    }
}

// Load all data
async function loadData() {
    await loadCurrentTournament();
    if (currentTournament) {
        await loadTeams();
        await loadMatches();
        displayTournamentInfo();
        displayRecentMatches();
    } else {
        await loadMostRecentTournament();
    }
}

// Load current live tournament
async function loadCurrentTournament() {
    currentTournament = await apiCall('/api/tournaments/live');
}

// Load most recent completed tournament if no live tournament
async function loadMostRecentTournament() {
    const completedTournaments = await apiCall('/api/tournaments/completed');
    if (completedTournaments && completedTournaments.length > 0) {
        // Get the most recent completed tournament
        currentTournament = completedTournaments.sort((a, b) => 
            new Date(b.created_at) - new Date(a.created_at)
        )[0];
        
        await loadTeams();
        await loadMatches();
        displayTournamentInfo();
        displayRecentMatches();
    } else {
        displayNoTournament();
    }
}

// Load teams for current tournament
async function loadTeams() {
    if (currentTournament) {
        teams = await apiCall(`/api/tournaments/${currentTournament.id}/teams`) || [];
    }
}

// Load matches for current tournament
async function loadMatches() {
    if (currentTournament) {
        matches = await apiCall(`/api/tournaments/${currentTournament.id}/matches`) || [];
        // Sort matches by start time (most recent first)
        matches.sort((a, b) => new Date(b.start_time) - new Date(a.start_time));
    }
}

// Display tournament information in hero section
function displayTournamentInfo() {
    if (!currentTournament) {
        displayNoTournament();
        return;
    }

    const statusText = currentTournament.status === 'live' ? 'LIVE NOW' :
                      currentTournament.status === 'upcoming' ? 'UPCOMING' : 'COMPLETED';

    // Update hero section elements
    document.getElementById('tournament-name').textContent = currentTournament.name;
    document.getElementById('tournament-subtitle').textContent = currentTournament.type;
    document.getElementById('tournament-description').textContent =
        `${statusText} - ${currentTournament.status === 'live' ? 'Watch the action unfold!' :
          currentTournament.status === 'upcoming' ? 'Get ready for the competition!' :
          'Tournament has concluded!'}`;

    // Update stats
    document.getElementById('teams-count').textContent = teams.length;
    document.getElementById('prize-pool').textContent = currentTournament.prize_pool ?
        `₹${currentTournament.prize_pool.toLocaleString()}` : '-';

    // Calculate tournament duration or days remaining
    const startDate = new Date(currentTournament.start_date);
    const endDate = currentTournament.end_date ? new Date(currentTournament.end_date) : null;
    const now = new Date();

    let daysText = '-';
    let daysLabel = 'DAYS';

    if (currentTournament.status === 'upcoming') {
        const daysUntilStart = Math.ceil((startDate - now) / (1000 * 60 * 60 * 24));
        daysText = daysUntilStart > 0 ? daysUntilStart : 0;
        daysLabel = 'DAYS TO GO';
    } else if (currentTournament.status === 'live' && endDate) {
        const daysRemaining = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
        daysText = daysRemaining > 0 ? daysRemaining : 0;
        daysLabel = 'DAYS LEFT';
    } else if (endDate && startDate) {
        const duration = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        daysText = duration;
        daysLabel = 'DURATION';
    }

    document.getElementById('tournament-days').textContent = daysText;
    document.getElementById('tournament-days-label').textContent = daysLabel;

    // Update tournament info grid
    displayTournamentInfoGrid();
}

// Display upcoming and recent matches
function displayRecentMatches() {
    displayUpcomingMatches();
    displayRecentResults();
}

// Display upcoming matches
function displayUpcomingMatches() {
    const upcomingMatchesElement = document.getElementById('upcoming-matches');

    if (!matches || matches.length === 0) {
        upcomingMatchesElement.innerHTML = '<div class="loading">No matches scheduled.</div>';
        return;
    }

    // Filter upcoming and live matches
    const upcomingMatches = matches.filter(match =>
        match.status === 'scheduled' || match.status === 'live'
    ).slice(0, 6);

    if (upcomingMatches.length === 0) {
        upcomingMatchesElement.innerHTML = '<div class="loading">No upcoming matches.</div>';
        return;
    }

    upcomingMatchesElement.innerHTML = upcomingMatches.map(match =>
        createMatchCard(match)
    ).join('');
}

// Display recent results
function displayRecentResults() {
    const recentResultsElement = document.getElementById('recent-results');

    if (!matches || matches.length === 0) {
        recentResultsElement.innerHTML = '<div class="loading">No results available.</div>';
        return;
    }

    // Filter completed matches
    const completedMatches = matches.filter(match =>
        match.status === 'completed'
    ).slice(0, 6);

    if (completedMatches.length === 0) {
        recentResultsElement.innerHTML = '<div class="loading">No completed matches yet.</div>';
        return;
    }

    recentResultsElement.innerHTML = completedMatches.map(match =>
        createMatchCard(match)
    ).join('');
}

// Helper function to get rank image path
function getRankImagePath(filename) {
    return filename ? `rank_png/${filename}` : '';
}

// Helper function to create player list with ranks
function createPlayerList(team, maxPlayers = 3) {
    if (!team) return '';

    let players = [];
    if (team.players && team.players.length > 0) {
        players = team.players;
    } else if (team.members && team.members.length > 0) {
        players = team.members.map(member => ({ name: member, rank: null }));
    }

    if (players.length === 0) return '';

    const displayPlayers = players.slice(0, maxPlayers);
    const remainingCount = players.length - maxPlayers;

    let playerList = displayPlayers.map(player => {
        const rankImg = player.rank ?
            `<img src="${getRankImagePath(player.rank)}" alt="Rank" class="rank-icon">` : '';
        return `${rankImg}${player.name}`;
    }).join(', ');

    if (remainingCount > 0) {
        playerList += ` +${remainingCount} more`;
    }

    return playerList;
}

// Create match card HTML
function createMatchCard(match) {
    const teamA = getTeamById(match.teamA);
    const teamB = getTeamById(match.teamB);
    const winner = match.winner ? getTeamById(match.winner) : null;

    return `
        <div class="match-card">
            <div class="match-header">
                <div class="match-time">${formatDate(match.start_time)}</div>
                <div class="match-status ${match.status}">${match.status.toUpperCase()}</div>
            </div>
            <div class="match-teams">
                <div class="team ${winner && winner.id === teamA?.id ? 'winner' : ''}">
                    <div class="team-logo">${teamA ? teamA.initials || teamA.name.substring(0, 2).toUpperCase() : 'TB'}</div>
                    <div class="team-name">${teamA ? teamA.name : 'TBD'}</div>
                    ${teamA ? `<div class="team-players">${createPlayerList(teamA)}</div>` : ''}
                </div>
                <div class="vs-divider">VS</div>
                <div class="team ${winner && winner.id === teamB?.id ? 'winner' : ''}">
                    <div class="team-name">${teamB ? teamB.name : 'TBD'}</div>
                    <div class="team-logo">${teamB ? teamB.initials || teamB.name.substring(0, 2).toUpperCase() : 'TB'}</div>
                    ${teamB ? `<div class="team-players">${createPlayerList(teamB)}</div>` : ''}
                </div>
            </div>
            ${match.status === 'completed' ? `
                <div class="match-score">
                    <div class="score">${match.scoreA || 0} - ${match.scoreB || 0}</div>
                </div>
            ` : ''}
            ${match.match_id ? `
                <div class="match-details">
                    <div class="match-map">Match ${match.match_id}</div>
                    <div class="match-round">Round ${match.round || 1}</div>
                </div>
            ` : ''}
        </div>
    `;
}

// Display tournament info grid
function displayTournamentInfoGrid() {
    const infoGridElement = document.getElementById('tournament-info-grid');

    if (!currentTournament || !infoGridElement) {
        return;
    }

    const startDate = currentTournament.start_date ?
        new Date(currentTournament.start_date).toLocaleDateString() : 'TBD';
    const endDate = currentTournament.end_date ?
        new Date(currentTournament.end_date).toLocaleDateString() : 'TBD';

    infoGridElement.innerHTML = `
        <div class="info-card">
            <h3>Tournament Format</h3>
            <p>${currentTournament.type}</p>
            <p>Best of 3 matches</p>
        </div>
        <div class="info-card">
            <h3>Schedule</h3>
            <p><strong>Start:</strong> ${startDate}</p>
            ${currentTournament.end_date ? `<p><strong>End:</strong> ${endDate}</p>` : ''}
        </div>
        <div class="info-card">
            <h3>Participation</h3>
            <p><strong>Teams:</strong> ${teams.length}</p>
            <p><strong>Matches:</strong> ${matches.length}</p>
        </div>
        <div class="info-card">
            <h3>Prize Pool</h3>
            <p>${currentTournament.prize_pool ?
                `₹${currentTournament.prize_pool.toLocaleString()}` :
                'To be announced'}</p>
        </div>
    `;
}

// Display no tournament message
function displayNoTournament() {
    // Update hero section
    document.getElementById('tournament-name').textContent = 'NO ACTIVE TOURNAMENT';
    document.getElementById('tournament-subtitle').textContent = 'STAY TUNED';
    document.getElementById('tournament-description').textContent =
        'Check back soon for upcoming Valorant tournaments and exciting matches!';

    // Reset stats
    document.getElementById('teams-count').textContent = '-';
    document.getElementById('prize-pool').textContent = '-';
    document.getElementById('tournament-days').textContent = '-';
    document.getElementById('tournament-days-label').textContent = 'DAYS';

    // Clear other sections
    document.getElementById('upcoming-matches').innerHTML =
        '<div class="loading">No upcoming matches scheduled.</div>';
    document.getElementById('recent-results').innerHTML =
        '<div class="loading">No recent results available.</div>';
    document.getElementById('tournament-info-grid').innerHTML = `
        <div class="info-card">
            <h3>Stay Updated</h3>
            <p>Follow us for tournament announcements</p>
        </div>
        <div class="info-card">
            <h3>Registration</h3>
            <p>Team registration opens soon</p>
        </div>
        <div class="info-card">
            <h3>Format</h3>
            <p>Single/Double elimination brackets</p>
        </div>
        <div class="info-card">
            <h3>Prizes</h3>
            <p>Exciting rewards await winners</p>
        </div>
    `;
}

// Helper function to get team by ID
function getTeamById(teamId) {
    return teams.find(team => team.id === teamId);
}

// Utility function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Add some visual effects
function addVisualEffects() {
    // Add parallax effect to hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const hero = document.querySelector('.hero-background');
        if (hero) {
            hero.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });
    
    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.match-card, .tournament-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Initialize visual effects after DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(addVisualEffects, 1000);
});
